// Datele jocului
const professions = [
    { name: "Doctor", emoji: "👨‍⚕️" },
    { name: "<PERSON><PERSON><PERSON>", emoji: "👨‍🏫" },
    { name: "<PERSON><PERSON><PERSON>", emoji: "👨‍🚒" },
    { name: "<PERSON><PERSON><PERSON><PERSON>", emoji: "👮‍♂️" },
    { name: "<PERSON><PERSON><PERSON><PERSON>", emoji: "👨‍🍳" },
    { name: "<PERSON>", emoji: "👨‍✈️" },
    { name: "<PERSON><PERSON><PERSON>", emoji: "👨‍🌾" },
    { name: "<PERSON><PERSON><PERSON>", emoji: "👨‍🔧" },
    { name: "Artist", emoji: "👨‍🎨" },
    { name: "<PERSON><PERSON><PERSON>", emoji: "👨‍🎤" }
];

// Starea jocului
let gameState = {
    currentTeam: 1,
    team1Score: 0,
    team2Score: 0,
    team1Professions: [],
    team2Professions: [],
    cardGrid: [], // Array cu 10 poziții pentru cartonașe
    selectedCard: null,
    selectedPosition: null,
    matches: [],
    gameStarted: false
};

// Elemente DOM
const elements = {
    startBtn: document.getElementById('start-game'),
    resetBtn: document.getElementById('reset-game'),
    cardsGrid: document.getElementById('cards-grid'),
    selectedCardSection: document.getElementById('selected-card-section'),
    selectedCard: document.getElementById('selected-card'),
    selectedNumber: document.getElementById('selected-number'),
    matchBtn: document.getElementById('match-btn'),
    noMatchBtn: document.getElementById('no-match-btn'),
    score1: document.getElementById('score1'),
    score2: document.getElementById('score2'),
    turnIndicator: document.getElementById('turn-indicator'),
    team1Professions: document.getElementById('team1-professions'),
    team2Professions: document.getElementById('team2-professions'),
    cardsLeft: document.getElementById('cards-left'),
    matchesContainer: document.getElementById('matches-container'),
    message: document.getElementById('message')
};

// Obiecte audio pentru sunete
const sounds = {
    success: null,
    error: null
};

// Funcție pentru încărcarea sunetelor
function loadSounds() {
    // Încercăm să încărcăm sunetul de succes
    const successFormats = ['mp3', 'wav', 'ogg'];
    const errorFormats = ['mp3', 'wav', 'ogg'];

    // Încărcăm sunetul de succes
    for (const format of successFormats) {
        try {
            sounds.success = new Audio(`sounds/success.${format}`);
            sounds.success.volume = 0.7; // Volum moderat
            sounds.success.preload = 'auto';
            break;
        } catch (e) {
            console.log(`Nu s-a putut încărca success.${format}`);
        }
    }

    // Încărcăm sunetul de eroare
    for (const format of errorFormats) {
        try {
            sounds.error = new Audio(`sounds/error.${format}`);
            sounds.error.volume = 0.7; // Volum moderat
            sounds.error.preload = 'auto';
            break;
        } catch (e) {
            console.log(`Nu s-a putut încărca error.${format}`);
        }
    }
}

// Funcții pentru redarea sunetelor
function playSuccessSound() {
    if (sounds.success) {
        try {
            sounds.success.currentTime = 0; // Resetăm la început
            sounds.success.play().catch(e => {
                console.log('Nu s-a putut reda sunetul de succes:', e);
            });
        } catch (e) {
            console.log('Eroare la redarea sunetului de succes:', e);
        }
    } else {
        console.log('Sunetul de succes nu este disponibil');
    }
}

function playErrorSound() {
    if (sounds.error) {
        try {
            sounds.error.currentTime = 0; // Resetăm la început
            sounds.error.play().catch(e => {
                console.log('Nu s-a putut reda sunetul de eroare:', e);
            });
        } catch (e) {
            console.log('Eroare la redarea sunetului de eroare:', e);
        }
    } else {
        console.log('Sunetul de eroare nu este disponibil');
    }
}

// Funcții utilitare
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

function showMessage(text, type = 'info') {
    elements.message.textContent = text;
    elements.message.className = `message ${type}`;
    setTimeout(() => {
        elements.message.textContent = '';
        elements.message.className = 'message';
    }, 3000);
}

// Funcții de joc
function initializeGame() {
    // Amestecăm meseriile și le împărțim în două echipe
    const shuffledProfessions = shuffleArray(professions);
    gameState.team1Professions = shuffledProfessions.slice(0, 5);
    gameState.team2Professions = shuffledProfessions.slice(5, 10);

    // Inițializăm grila de cartonașe
    initializeCardGrid();

    // Resetăm starea
    gameState.currentTeam = 1;
    gameState.team1Score = 0;
    gameState.team2Score = 0;
    gameState.matches = [];
    gameState.selectedCard = null;
    gameState.selectedPosition = null;
    gameState.gameStarted = true;

    updateDisplay();
    showMessage('Jocul a început! Echipa 1 începe!', 'success');
}

function initializeCardGrid() {
    // Creăm grila cu 10 poziții
    gameState.cardGrid = [];
    const shuffledProfessions = shuffleArray([...professions]);

    for (let i = 0; i < 10; i++) {
        gameState.cardGrid.push({
            position: i + 1,
            profession: shuffledProfessions[i],
            isRevealed: false,
            isMatched: false,
            displayNumber: i + 1
        });
    }
}

function updateDisplay() {
    // Actualizăm scorul
    elements.score1.textContent = gameState.team1Score;
    elements.score2.textContent = gameState.team2Score;

    // Actualizăm indicatorul de rând
    elements.turnIndicator.textContent = `Rândul echipei ${gameState.currentTeam}`;

    // Actualizăm meseriile echipelor
    updateTeamProfessions();

    // Actualizăm grila de cartonașe
    updateCardsGrid();

    // Actualizăm numărul de cartonașe rămase
    const remainingCards = gameState.cardGrid.filter(card => !card.isMatched).length;
    elements.cardsLeft.textContent = remainingCards;

    // Actualizăm potrivirile
    updateMatches();

    // Ascundem secțiunea de cartonaș selectat dacă nu avem cartonaș
    if (!gameState.selectedCard) {
        elements.selectedCardSection.style.display = 'none';
    }
}

function updateCardsGrid() {
    elements.cardsGrid.innerHTML = '';

    gameState.cardGrid.forEach((card, index) => {
        const cardElement = document.createElement('div');
        cardElement.className = 'card-item';
        cardElement.dataset.position = index;

        if (card.isMatched) {
            // Cartonașul a fost potrivit - arată imaginea și nu mai poate fi selectat
            cardElement.classList.add('face-up');
            cardElement.textContent = card.profession.emoji;
        } else if (card.isRevealed) {
            // Cartonașul este temporar întors (pentru selecție)
            cardElement.classList.add('face-up');
            cardElement.textContent = card.profession.emoji;
            cardElement.style.cursor = 'default';
        } else {
            // Cartonașul este cu fața în jos - arată numărul
            cardElement.classList.add('face-down');
            cardElement.textContent = card.displayNumber;
            cardElement.addEventListener('click', () => selectCard(index));
        }

        elements.cardsGrid.appendChild(cardElement);
    });
}

function updateTeamProfessions() {
    // Echipa 1
    elements.team1Professions.innerHTML = '';
    gameState.team1Professions.forEach(profession => {
        const div = document.createElement('div');
        div.className = 'profession-item';
        div.textContent = profession.name;

        // Verificăm dacă meseria a fost potrivită
        const isMatched = gameState.matches.some(match => match.name === profession.name);
        if (isMatched) {
            div.classList.add('matched');
        }

        elements.team1Professions.appendChild(div);
    });

    // Echipa 2
    elements.team2Professions.innerHTML = '';
    gameState.team2Professions.forEach(profession => {
        const div = document.createElement('div');
        div.className = 'profession-item';
        div.textContent = profession.name;

        // Verificăm dacă meseria a fost potrivită
        const isMatched = gameState.matches.some(match => match.name === profession.name);
        if (isMatched) {
            div.classList.add('matched');
        }

        elements.team2Professions.appendChild(div);
    });
}

function updateMatches() {
    elements.matchesContainer.innerHTML = '';
    gameState.matches.forEach(match => {
        const div = document.createElement('div');
        div.className = 'match-item';
        div.innerHTML = `
            <span class="emoji">${match.emoji}</span>
            <span class="profession">${match.name}</span>
        `;
        elements.matchesContainer.appendChild(div);
    });
}

function selectCard(position) {
    if (!gameState.gameStarted) {
        showMessage('Începe mai întâi jocul!', 'error');
        return;
    }

    if (gameState.selectedCard) {
        showMessage('Ai deja un cartonaș selectat! Alege dacă se potrivește sau nu.', 'error');
        return;
    }

    const card = gameState.cardGrid[position];

    if (card.isMatched || card.isRevealed) {
        showMessage('Acest cartonaș nu poate fi selectat!', 'error');
        return;
    }

    // Selectăm cartonașul
    gameState.selectedCard = card;
    gameState.selectedPosition = position;
    card.isRevealed = true;

    // Afișăm cartonașul selectat
    elements.selectedCard.textContent = card.profession.emoji;
    elements.selectedNumber.textContent = card.displayNumber;
    elements.selectedCardSection.style.display = 'block';

    // Animație pentru cartonaș
    setTimeout(() => {
        const cardElement = document.querySelector(`[data-position="${position}"]`);
        if (cardElement) {
            cardElement.classList.add('bounce');
            setTimeout(() => cardElement.classList.remove('bounce'), 600);
        }
    }, 100);

    updateDisplay();
}

function reshuffleCards() {
    // Amestecăm din nou numerele pentru cartonașele care nu sunt potrivite
    const availableNumbers = [];
    const matchedPositions = [];

    // Identificăm pozițiile libere și cele ocupate
    gameState.cardGrid.forEach((card, index) => {
        if (card.isMatched) {
            matchedPositions.push(index);
        } else {
            availableNumbers.push(card.displayNumber);
        }
    });

    // Amestecăm numerele disponibile
    const shuffledNumbers = shuffleArray(availableNumbers);
    let numberIndex = 0;

    // Redistribuim numerele
    gameState.cardGrid.forEach((card, index) => {
        if (!card.isMatched) {
            card.displayNumber = shuffledNumbers[numberIndex];
            card.isRevealed = false;
            numberIndex++;
        }
    });
}

function handleMatch() {
    if (!gameState.selectedCard) return;

    const currentTeamProfessions = gameState.currentTeam === 1 ?
        gameState.team1Professions : gameState.team2Professions;

    // Verificăm dacă cartonașul se potrivește cu o meserie a echipei curente
    const matchingProfession = currentTeamProfessions.find(p => p.name === gameState.selectedCard.profession.name);

    if (matchingProfession) {
        // Potrivire corectă!
        playSuccessSound();

        if (gameState.currentTeam === 1) {
            gameState.team1Score += 100;
        } else {
            gameState.team2Score += 100;
        }

        // Marcăm cartonașul ca potrivit
        gameState.selectedCard.isMatched = true;
        gameState.selectedCard.isRevealed = false;

        // Adăugăm potrivirea
        gameState.matches.push(gameState.selectedCard.profession);

        showMessage(`Bravo! Potrivire corectă! +100 puncte pentru echipa ${gameState.currentTeam}!`, 'success');

        // Animație de succes
        elements.selectedCard.classList.add('bounce');
        setTimeout(() => elements.selectedCard.classList.remove('bounce'), 600);
    } else {
        // Potrivire incorectă
        playErrorSound();

        if (gameState.currentTeam === 1) {
            gameState.team1Score += 50;
        } else {
            gameState.team2Score += 50;
        }

        showMessage(`Potrivire incorectă! +50 puncte pentru echipa ${gameState.currentTeam}. Cartonașele se amestecă din nou.`, 'error');

        // Animație de eroare
        elements.selectedCard.classList.add('shake');
        setTimeout(() => elements.selectedCard.classList.remove('shake'), 500);

        // Amestecăm din nou cartonașele după o scurtă întârziere
        setTimeout(() => {
            reshuffleCards();
            updateDisplay();
        }, 1000);
    }

    // Resetăm cartonașul selectat
    gameState.selectedCard = null;
    gameState.selectedPosition = null;

    // Schimbăm echipa
    gameState.currentTeam = gameState.currentTeam === 1 ? 2 : 1;

    updateDisplay();
    checkGameEnd();
}

function handleNoMatch() {
    if (!gameState.selectedCard) return;

    playErrorSound();

    // Echipa primește 50 de puncte
    if (gameState.currentTeam === 1) {
        gameState.team1Score += 50;
    } else {
        gameState.team2Score += 50;
    }

    showMessage(`Cartonașul se întoarce cu fața în jos! +50 puncte pentru echipa ${gameState.currentTeam}! Cartonașele se amestecă.`, 'info');

    // Animație
    elements.selectedCard.classList.add('shake');
    setTimeout(() => elements.selectedCard.classList.remove('shake'), 500);

    // Amestecăm din nou cartonașele după o scurtă întârziere
    setTimeout(() => {
        reshuffleCards();
        updateDisplay();
    }, 1000);

    // Resetăm cartonașul selectat
    gameState.selectedCard = null;
    gameState.selectedPosition = null;

    // Schimbăm echipa
    gameState.currentTeam = gameState.currentTeam === 1 ? 2 : 1;

    updateDisplay();
    checkGameEnd();
}

function checkGameEnd() {
    if (gameState.matches.length === 10) {
        // Jocul s-a terminat - toate cartonașele au fost potrivite
        gameState.gameStarted = false;

        let winner;
        if (gameState.team1Score > gameState.team2Score) {
            winner = "Echipa 1";
        } else if (gameState.team2Score > gameState.team1Score) {
            winner = "Echipa 2";
        } else {
            winner = "Egalitate";
        }

        setTimeout(() => {
            showMessage(`🎉 Jocul s-a terminat! Câștigător: ${winner}! 🎉`, 'success');
        }, 1000);
    }
}

function resetGame() {
    gameState = {
        currentTeam: 1,
        team1Score: 0,
        team2Score: 0,
        team1Professions: [],
        team2Professions: [],
        cardGrid: [],
        selectedCard: null,
        selectedPosition: null,
        matches: [],
        gameStarted: false
    };

    updateDisplay();
    showMessage('Jocul a fost resetat!', 'info');
}

// Funcție pentru testarea sunetelor
function testSounds() {
    showMessage('Testez sunetele...', 'info');

    setTimeout(() => {
        showMessage('🔊 Sunet de succes...', 'success');
        playSuccessSound();
    }, 500);

    setTimeout(() => {
        showMessage('🔊 Sunet de eroare...', 'error');
        playErrorSound();
    }, 2000);

    setTimeout(() => {
        const successAvailable = sounds.success ? '✅' : '❌';
        const errorAvailable = sounds.error ? '✅' : '❌';
        showMessage(`Testare completă! Succes: ${successAvailable} | Eroare: ${errorAvailable}`, 'info');
    }, 3500);
}

// Event listeners
elements.startBtn.addEventListener('click', initializeGame);
elements.resetBtn.addEventListener('click', resetGame);
elements.matchBtn.addEventListener('click', handleMatch);
elements.noMatchBtn.addEventListener('click', handleNoMatch);
document.getElementById('test-sounds').addEventListener('click', testSounds);

// Inițializare
loadSounds(); // Încărcăm sunetele
updateDisplay();
