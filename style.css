* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 95vw;
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

header {
    text-align: center;
    margin-bottom: 30px;
    background: white;
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

header h1 {
    font-size: 3.5em;
    color: #4a90e2;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

header p {
    font-size: 1.5em;
    color: #666;
}

.game-board {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-rows: auto auto auto auto;
    gap: 30px;
    grid-template-areas:
        "scoreboard scoreboard scoreboard"
        "turn-indicator turn-indicator turn-indicator"
        "team1-professions cards-section team2-professions"
        "matches matches matches"
        "controls controls controls"
        "sound-info sound-info sound-info"
        "messages messages messages";
}

.scoreboard {
    grid-area: scoreboard;
    display: flex;
    justify-content: space-around;
    gap: 30px;
}

.team {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    color: white;
    flex: 1;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.team-1 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.team-2 {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.team h2 {
    font-size: 2em;
    margin-bottom: 15px;
}

.score {
    font-size: 1.8em;
    font-weight: bold;
}

.current-turn {
    grid-area: turn-indicator;
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.current-turn h3 {
    font-size: 2em;
    color: #333;
}

.team-professions {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 15px;
    border: 3px solid #e9ecef;
    height: fit-content;
}

.team-professions.team1 {
    grid-area: team1-professions;
}

.team-professions.team2 {
    grid-area: team2-professions;
}

.team-professions h3 {
    color: #4a90e2;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5em;
}

.professions-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.profession-item {
    background: white;
    padding: 15px;
    border-radius: 10px;
    border: 2px solid #dee2e6;
    text-align: center;
    font-weight: bold;
    font-size: 1.1em;
    transition: all 0.3s ease;
}

.profession-item.matched {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.profession-emoji {
    display: inline-block;
    font-size: 1.5em;
    margin-left: 10px;
    animation: bounceIn 0.5s ease;
}

@keyframes bounceIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.cards-grid-section {
    grid-area: cards-section;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.cards-grid-section h3 {
    color: #4a90e2;
    margin-bottom: 20px;
    font-size: 2em;
}

.cards-count {
    font-size: 1.3em;
    margin-bottom: 25px;
    color: #666;
    font-weight: bold;
}

.cards-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 20px;
    border: 3px solid #e9ecef;
}

.card-item {
    width: 120px;
    height: 120px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    font-size: 1.8em;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.card-item.face-down {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 3px solid #4a90e2;
}

.card-item.face-down:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.card-item.face-down:active {
    transform: scale(0.95);
}

.card-item.face-up {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #333;
    border: 3px solid #28a745;
    font-size: 4em;
    cursor: default;
}

.card-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #6c757d;
    color: #fff;
}

.card-item.disabled:hover {
    transform: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.selected-card-section {
    text-align: center;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 15px;
    margin-top: 20px;
}

.selected-card-display {
    margin-bottom: 20px;
}

.selected-card-number {
    font-size: 1.5em;
    font-weight: bold;
    color: #4a90e2;
    margin-bottom: 20px;
}

.selected-card {
    font-size: 5em;
    margin: 25px 0;
    padding: 30px;
    background: white;
    border-radius: 20px;
    display: inline-block;
    box-shadow: 0 6px 25px rgba(0,0,0,0.15);
    min-width: 150px;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px solid #4a90e2;
}

.match-buttons {
    display: flex;
    gap: 30px;
    justify-content: center;
    margin-top: 25px;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1.3em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    min-width: 180px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.btn-success {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #333;
}

.btn-danger {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: #333;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
}

.matches-section {
    grid-area: matches;
}

.matches-section h3 {
    color: #4a90e2;
    margin-bottom: 25px;
    text-align: center;
    font-size: 2em;
}

.matches-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.match-item {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    border: 3px solid #28a745;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.match-item .emoji {
    font-size: 3em;
    display: block;
    margin-bottom: 15px;
}

.match-item .profession {
    font-weight: bold;
    color: #155724;
    font-size: 1.2em;
}

.controls {
    grid-area: controls;
    text-align: center;
}

.controls .btn {
    margin: 0 15px;
}

.sound-info {
    grid-area: sound-info;
    background: #e3f2fd;
    border: 3px solid #2196f3;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
}

.sound-info p {
    margin: 10px 0;
    color: #1565c0;
    font-size: 1.1em;
}

.sound-info code {
    background: #bbdefb;
    padding: 4px 8px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    color: #0d47a1;
    font-size: 1em;
}

.message {
    grid-area: messages;
    text-align: center;
    padding: 20px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 1.3em;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 2px solid #28a745;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 2px solid #dc3545;
}

.message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 2px solid #17a2b8;
}

/* Animații */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.bounce {
    animation: bounce 0.6s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s;
}

/* Flying card animation */
@keyframes flyToMatch {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(10deg);
        opacity: 0.9;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

.flying-card {
    position: fixed;
    z-index: 1000;
    pointer-events: none;
    width: 120px;
    height: 120px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 4em;
    box-shadow: 0 8px 30px rgba(0,0,0,0.3);
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #333;
    border: 3px solid #28a745;
    animation: flyToMatch 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.flying-card.enhanced {
    box-shadow: 0 12px 40px rgba(0,0,0,0.4), 0 0 20px rgba(132, 250, 176, 0.6);
    filter: brightness(1.1);
}

.flying-card.landed {
    position: relative;
    z-index: 1;
    width: auto;
    height: auto;
    font-size: 3em;
    margin: 0;
    padding: 20px;
    animation: none;
    transform: none;
}

/* Responsive */
@media (max-width: 768px) {
    .teams-professions {
        flex-direction: column;
    }

    .scoreboard {
        flex-direction: column;
    }

    .match-buttons {
        flex-direction: column;
        align-items: center;
    }

    .controls .btn {
        display: block;
        margin: 10px auto;
        width: 200px;
    }

    .cards-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 300px;
        gap: 10px;
    }

    .card-item {
        width: 60px;
        height: 60px;
        font-size: 1em;
    }

    .card-item.face-up {
        font-size: 2em;
    }
}

@media (max-width: 480px) {
    .cards-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 200px;
    }
}
